import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import Button from '../components/atoms/Button';
import {
  Brain,
  Cpu,
  Zap,
  Network,
  BarChart3,
  ArrowRight,
  Calculator,
  Atom,
  Layers,
  TrendingUp,
  Clock,
  Database,
  CheckCircle,
  BookOpen,
  Award,
  Users
} from 'lucide-react';

const TechnicalFoundation: React.FC = () => {
  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-24">
        {/* Hero Section */}
        <div className="container mx-auto px-8 py-16">
          <div className="text-center mb-16">
            <Typography as="h1" variant="4xl" weight="bold" gradient="consciousness" className="mb-6">
              Technical Foundation - Mathematical Framework
            </Typography>
            <Typography as="p" variant="xl" color="secondary" className="max-w-4xl mx-auto mb-8">
              Beyond Traditional AI: The Mathematical Revolution
            </Typography>
            <Typography as="p" variant="lg" color="tertiary" className="max-w-5xl mx-auto leading-relaxed">
              Artificial Cellular Intelligence is built on rigorous mathematical foundations that transcend conventional machine learning. 
              Our framework combines cutting-edge concepts from nonlinear dynamics, fractional calculus, and complex systems theory 
              to create truly adaptive intelligence.
            </Typography>
          </div>

          {/* Core Mathematical Blueprint */}
          <Card variant="quantum" className="mb-16 p-12 border-consciousness/30">
            <Typography as="h2" variant="3xl" weight="bold" color="consciousness" className="mb-8">
              The Core Mathematical Blueprint
            </Typography>
            
            <Card variant="neural" className="mb-8 p-8 border-consciousness/20">
              <Typography as="h3" variant="2xl" weight="semibold" color="consciousness" className="mb-6">
                Fractional Reaction-Diffusion Systems
              </Typography>
              <Typography variant="lg" color="secondary" className="mb-6">
                At the heart of ACI lies a sophisticated mathematical framework that models intelligence as an emergent property 
                of interconnected computational "cells."
              </Typography>
              
              <div className="bg-abyssal-deep/50 p-6 rounded-xl border border-consciousness/20 mb-6">
                <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                  The Core Equation of Motion:
                </Typography>
                <div className="font-mono text-consciousness bg-abyssal-elevated/50 p-4 rounded-lg border border-consciousness/30">
                  ∂x_i/∂t = f(x_i) + g(x_i, Φ(r_i, t)) - κ(-Δ)^α/2 x_i + σ dW_i/dt
                </div>
              </div>

              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                Breaking Down Each Component:
              </Typography>
              
              <div className="grid md:grid-cols-2 gap-6">
                <Card variant="consciousness" className="p-6 border-consciousness/25">
                  <div className="flex items-start gap-3 mb-4">
                    <Brain className="w-6 h-6 text-consciousness mt-1" />
                    <Typography variant="lg" weight="semibold" color="consciousness">f(x_i): Intrinsic Cell Dynamics</Typography>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-consciousness rounded-full mt-2 flex-shrink-0"></div>
                      <Typography variant="sm" color="secondary">Models autonomous behavior of individual computational cells</Typography>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-consciousness rounded-full mt-2 flex-shrink-0"></div>
                      <Typography variant="sm" color="secondary">Creates bistability for decision-making and oscillation for timing</Typography>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-consciousness rounded-full mt-2 flex-shrink-0"></div>
                      <Typography variant="sm" color="secondary">Enables cells to maintain memory and exhibit agency</Typography>
                    </div>
                  </div>
                </Card>

                <Card variant="creativity" className="p-6 border-creativity/25">
                  <div className="flex items-start gap-3 mb-4">
                    <Network className="w-6 h-6 text-creativity mt-1" />
                    <Typography variant="lg" weight="semibold" color="creativity">g(x_i, Φ(r_i, t)): Field Coupling</Typography>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-creativity rounded-full mt-2 flex-shrink-0"></div>
                      <Typography variant="sm" color="secondary">Describes interaction with the collective information field</Typography>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-creativity rounded-full mt-2 flex-shrink-0"></div>
                      <Typography variant="sm" color="secondary">Enables indirect communication through environmental modification</Typography>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-creativity rounded-full mt-2 flex-shrink-0"></div>
                      <Typography variant="sm" color="secondary">Creates emergent coordination without central control</Typography>
                    </div>
                  </div>
                </Card>

                <Card variant="intuition" className="p-6 border-harmony/25">
                  <div className="flex items-start gap-3 mb-4">
                    <Layers className="w-6 h-6 text-harmony mt-1" />
                    <Typography variant="lg" weight="semibold" color="harmony">κ(-Δ)^α/2 x_i: Fractional Diffusion</Typography>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-harmony rounded-full mt-2 flex-shrink-0"></div>
                      <Typography variant="sm" color="secondary">Revolutionary non-local interaction mechanism</Typography>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-harmony rounded-full mt-2 flex-shrink-0"></div>
                      <Typography variant="sm" color="secondary">Enables long-range correlations and rapid information propagation</Typography>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-harmony rounded-full mt-2 flex-shrink-0"></div>
                      <Typography variant="sm" color="secondary">Parameter α controls interaction reach from local (α=2) to global (α→0)</Typography>
                    </div>
                  </div>
                </Card>

                <Card variant="neural" className="p-6 border-consciousness/25">
                  <div className="flex items-start gap-3 mb-4">
                    <Zap className="w-6 h-6 text-consciousness mt-1" />
                    <Typography variant="lg" weight="semibold" color="consciousness">σ dW_i/dt: Stochastic Innovation</Typography>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-consciousness rounded-full mt-2 flex-shrink-0"></div>
                      <Typography variant="sm" color="secondary">Introduces controlled randomness for exploration and adaptation</Typography>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-consciousness rounded-full mt-2 flex-shrink-0"></div>
                      <Typography variant="sm" color="secondary">Enables escape from local optima and creative problem-solving</Typography>
                    </div>
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-consciousness rounded-full mt-2 flex-shrink-0"></div>
                      <Typography variant="sm" color="secondary">Balances stability with adaptability at the "edge of chaos"</Typography>
                    </div>
                  </div>
                </Card>
              </div>
            </Card>
          </Card>

          {/* Non-Extensive Statistical Mechanics */}
          <Card variant="neural" className="mb-16 p-12 border-consciousness/20">
            <Typography as="h2" variant="2xl" weight="bold" color="consciousness" className="mb-8">
              Non-Extensive Statistical Mechanics
            </Typography>
            
            <Typography as="h3" variant="xl" weight="semibold" color="creativity" className="mb-6">
              Beyond Boltzmann-Gibbs Statistics
            </Typography>
            <Typography variant="lg" color="secondary" className="mb-6">
              Traditional AI assumes statistical independence. ACI operates on Tsallis non-extensive statistics, 
              designed for systems with long-range correlations:
            </Typography>
            
            <div className="bg-abyssal-deep/50 p-6 rounded-xl border border-consciousness/20 mb-6">
              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                Tsallis Entropy:
              </Typography>
              <div className="font-mono text-consciousness bg-abyssal-elevated/50 p-4 rounded-lg border border-consciousness/30">
                S_q = k * (1 - Σ_i p_i^q) / (q - 1)
              </div>
            </div>

            <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
              Key Advantages:
            </Typography>
            
            <div className="grid md:grid-cols-3 gap-6">
              <Card variant="consciousness" className="p-6 border-consciousness/25">
                <BarChart3 className="w-8 h-8 text-consciousness mb-4" />
                <Typography variant="lg" weight="semibold" color="consciousness" className="mb-2">
                  Natural Power-Law Distributions
                </Typography>
                <Typography variant="sm" color="secondary">
                  Mirrors real-world complex systems
                </Typography>
              </Card>

              <Card variant="creativity" className="p-6 border-creativity/25">
                <TrendingUp className="w-8 h-8 text-creativity mb-4" />
                <Typography variant="lg" weight="semibold" color="creativity" className="mb-2">
                  Scale-Free Behavior
                </Typography>
                <Typography variant="sm" color="secondary">
                  Consistent performance across different scales
                </Typography>
              </Card>

              <Card variant="intuition" className="p-6 border-harmony/25">
                <Clock className="w-8 h-8 text-harmony mb-4" />
                <Typography variant="lg" weight="semibold" color="harmony" className="mb-2">
                  Heavy-Tailed Dynamics
                </Typography>
                <Typography variant="sm" color="secondary">
                  Captures rare but significant events
                </Typography>
              </Card>
            </div>
          </Card>

          {/* Renormalization Group Theory */}
          <Card variant="quantum" className="mb-16 p-12 border-consciousness/30">
            <Typography as="h2" variant="2xl" weight="bold" color="consciousness" className="mb-8">
              Renormalization Group Theory
            </Typography>

            <Typography as="h3" variant="xl" weight="semibold" color="creativity" className="mb-6">
              Multi-Scale Coherence
            </Typography>
            <Typography variant="lg" color="secondary" className="mb-6">
              ACI ensures intelligence principles operate consistently across all scales:
            </Typography>

            <div className="grid md:grid-cols-2 gap-8 mb-8">
              <Card variant="consciousness" className="p-6 border-consciousness/25">
                <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                  Scale Invariance:
                </Typography>
                <Typography variant="sm" color="secondary">
                  The system behavior remains coherent whether observing individual cells or the entire network.
                </Typography>
              </Card>

              <Card variant="creativity" className="p-6 border-creativity/25">
                <Typography variant="lg" weight="semibold" color="creativity" className="mb-4">
                  Fixed Point Operation:
                </Typography>
                <Typography variant="sm" color="secondary" className="mb-3">
                  ACI is tuned to operate near critical points where:
                </Typography>
                <div className="space-y-2">
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-creativity mt-1 flex-shrink-0" />
                    <Typography variant="sm" color="secondary">Small changes can trigger large-scale reorganization</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-creativity mt-1 flex-shrink-0" />
                    <Typography variant="sm" color="secondary">System maintains stability while remaining highly adaptive</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <CheckCircle className="w-4 h-4 text-creativity mt-1 flex-shrink-0" />
                    <Typography variant="sm" color="secondary">Intelligence emerges naturally without explicit programming</Typography>
                  </div>
                </div>
              </Card>
            </div>
          </Card>

          {/* Practical Implementation */}
          <Card variant="neural" className="mb-16 p-12 border-consciousness/20">
            <Typography as="h2" variant="2xl" weight="bold" color="consciousness" className="mb-8">
              Practical Implementation
            </Typography>

            <Typography as="h3" variant="xl" weight="semibold" color="creativity" className="mb-6">
              From Theory to Technology
            </Typography>
            <Typography variant="lg" color="secondary" className="mb-8">
              Our mathematical framework translates into:
            </Typography>

            <div className="grid md:grid-cols-2 gap-8 mb-8">
              <div className="space-y-6">
                <Card variant="consciousness" className="p-6 border-consciousness/25">
                  <Cpu className="w-8 h-8 text-consciousness mb-4" />
                  <Typography variant="lg" weight="semibold" color="consciousness" className="mb-2">
                    Lightweight Computation
                  </Typography>
                  <Typography variant="sm" color="secondary">
                    Efficient algorithms that scale from IoT devices to cloud infrastructure
                  </Typography>
                </Card>

                <Card variant="creativity" className="p-6 border-creativity/25">
                  <Clock className="w-8 h-8 text-creativity mb-4" />
                  <Typography variant="lg" weight="semibold" color="creativity" className="mb-2">
                    Real-Time Adaptation
                  </Typography>
                  <Typography variant="sm" color="secondary">
                    Continuous learning without expensive retraining
                  </Typography>
                </Card>
              </div>

              <div className="space-y-6">
                <Card variant="intuition" className="p-6 border-harmony/25">
                  <CheckCircle className="w-8 h-8 text-harmony mb-4" />
                  <Typography variant="lg" weight="semibold" color="harmony" className="mb-2">
                    Robust Performance
                  </Typography>
                  <Typography variant="sm" color="secondary">
                    Graceful degradation and self-healing capabilities
                  </Typography>
                </Card>

                <Card variant="neural" className="p-6 border-consciousness/25">
                  <Brain className="w-8 h-8 text-consciousness mb-4" />
                  <Typography variant="lg" weight="semibold" color="consciousness" className="mb-2">
                    Emergent Intelligence
                  </Typography>
                  <Typography variant="sm" color="secondary">
                    Complex behaviors arising from simple local rules
                  </Typography>
                </Card>
              </div>
            </div>

            <Card variant="quantum" className="p-8 border-consciousness/30">
              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-6">
                Technical Specifications:
              </Typography>

              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <BarChart3 className="w-5 h-5 text-consciousness mt-1" />
                    <div>
                      <Typography variant="sm" weight="semibold" color="consciousness">Computational Complexity:</Typography>
                      <Typography variant="sm" color="secondary">O(N log N) for N-cell systems</Typography>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Database className="w-5 h-5 text-consciousness mt-1" />
                    <div>
                      <Typography variant="sm" weight="semibold" color="consciousness">Memory Requirements:</Typography>
                      <Typography variant="sm" color="secondary">Linear scaling with network size</Typography>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-start gap-3">
                    <Clock className="w-5 h-5 text-consciousness mt-1" />
                    <div>
                      <Typography variant="sm" weight="semibold" color="consciousness">Convergence Time:</Typography>
                      <Typography variant="sm" color="secondary">Typically 10² to 10³ iterations</Typography>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-consciousness mt-1" />
                    <div>
                      <Typography variant="sm" weight="semibold" color="consciousness">Stability Guarantee:</Typography>
                      <Typography variant="sm" color="secondary">Lyapunov stable under specified parameter ranges</Typography>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </Card>

          {/* Research Foundation */}
          <Card variant="quantum" className="mb-16 p-12 border-consciousness/30">
            <Typography as="h2" variant="2xl" weight="bold" color="consciousness" className="mb-8">
              Research Foundation
            </Typography>

            <Typography as="h3" variant="xl" weight="semibold" color="creativity" className="mb-6">
              Built on Decades of Scientific Research
            </Typography>
            <Typography variant="lg" color="secondary" className="mb-8">
              Our mathematical framework draws from:
            </Typography>

            <div className="grid md:grid-cols-2 gap-8 mb-8">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <Network className="w-6 h-6 text-consciousness mt-1" />
                  <div>
                    <Typography variant="lg" weight="semibold" color="consciousness">Complex Systems Theory</Typography>
                    <Typography variant="sm" color="secondary">Santa Fe Institute methodologies</Typography>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <Brain className="w-6 h-6 text-creativity mt-1" />
                  <div>
                    <Typography variant="lg" weight="semibold" color="creativity">Biological Mathematics</Typography>
                    <Typography variant="sm" color="secondary">Proven natural algorithms</Typography>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <Calculator className="w-6 h-6 text-harmony mt-1" />
                  <div>
                    <Typography variant="lg" weight="semibold" color="harmony">Advanced Calculus</Typography>
                    <Typography variant="sm" color="secondary">Fractional and stochastic calculus</Typography>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <Atom className="w-6 h-6 text-consciousness mt-1" />
                  <div>
                    <Typography variant="lg" weight="semibold" color="consciousness">Statistical Physics</Typography>
                    <Typography variant="sm" color="secondary">Non-equilibrium thermodynamics</Typography>
                  </div>
                </div>
              </div>
            </div>

            <Card variant="neural" className="p-8 border-consciousness/20">
              <Typography variant="lg" weight="semibold" color="consciousness" className="mb-6">
                Peer-Reviewed Validation:
              </Typography>

              <div className="grid md:grid-cols-3 gap-6">
                <div className="flex items-start gap-3">
                  <BookOpen className="w-6 h-6 text-consciousness mt-1" />
                  <div>
                    <Typography variant="sm" weight="semibold" color="consciousness">15+ published papers</Typography>
                    <Typography variant="sm" color="secondary">in top-tier journals</Typography>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <Users className="w-6 h-6 text-creativity mt-1" />
                  <div>
                    <Typography variant="sm" weight="semibold" color="creativity">Collaboration</Typography>
                    <Typography variant="sm" color="secondary">with leading research institutions</Typography>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <Award className="w-6 h-6 text-harmony mt-1" />
                  <div>
                    <Typography variant="sm" weight="semibold" color="harmony">Continuous validation</Typography>
                    <Typography variant="sm" color="secondary">against established benchmarks</Typography>
                  </div>
                </div>
              </div>
            </Card>
          </Card>

          {/* Call to Action */}
          <div className="text-center">
            <Card variant="consciousness" className="p-12 border-consciousness/25">
              <Typography as="h3" variant="2xl" weight="bold" color="consciousness" className="mb-6">
                Explore the Mathematical Revolution
              </Typography>
              <Typography variant="lg" color="secondary" className="mb-8 max-w-3xl mx-auto">
                Discover how our rigorous mathematical foundations enable unprecedented AI capabilities.
              </Typography>

              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <Button
                  variant="quantum"
                  size="xl"
                  leftIcon={<Brain className="w-6 h-6" />}
                  rightIcon={<ArrowRight className="w-5 h-5" />}
                  onClick={() => window.location.href = '/innovation-engine'}
                  className="min-w-[200px] font-semibold"
                >
                  Innovation Engine
                </Button>

                <Button
                  variant="professional"
                  size="xl"
                  leftIcon={<Calculator className="w-6 h-6" />}
                  onClick={() => window.location.href = '/competitive-advantage'}
                  className="min-w-[200px] font-semibold"
                >
                  Competitive Advantage
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default TechnicalFoundation;
