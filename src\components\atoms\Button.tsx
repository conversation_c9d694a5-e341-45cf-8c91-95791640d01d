import React, { forwardRef } from 'react';
import { cn } from '@/lib/utils';
import { cva, type VariantProps } from 'class-variance-authority';

// Bio-Quantum Button Styles - CSS-in-JS for custom property integration
const bioQuantumButtonStyles = `
  .btn-primary {
    background: linear-gradient(to right, var(--consciousness-primary), var(--consciousness-secondary));
    box-shadow: 0 10px 25px -5px rgba(0, 229, 255, 0.25);
    border-color: var(--consciousness-primary);
  }
  .btn-primary:hover {
    background: linear-gradient(to right, var(--consciousness-secondary), var(--consciousness-primary));
    box-shadow: 0 20px 40px -5px rgba(0, 229, 255, 0.3);
  }
  .btn-primary:focus {
    --tw-ring-color: var(--consciousness-primary);
  }

  .btn-quantum {
    background: linear-gradient(135deg, rgba(0, 229, 255, 0.95), rgba(0, 255, 194, 0.95));
    box-shadow: 0 8px 32px -8px rgba(0, 229, 255, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    border: 1px solid rgba(0, 229, 255, 0.3);
    backdrop-filter: blur(12px);
    position: relative;
    overflow: hidden;
  }
  .btn-quantum:hover {
    background: linear-gradient(135deg, rgba(0, 255, 194, 0.98), rgba(0, 229, 255, 0.98));
    box-shadow: 0 12px 48px -8px rgba(0, 229, 255, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.15) inset;
    transform: translateY(-1px);
  }
  .btn-quantum:active {
    transform: translateY(0px);
    box-shadow: 0 4px 16px -4px rgba(0, 229, 255, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  }
  .btn-quantum:focus {
    --tw-ring-color: rgba(0, 229, 255, 0.5);
    --tw-ring-offset-width: 2px;
  }
  .btn-quantum::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
  }
  .btn-quantum:hover::before {
    left: 100%;
  }

  .btn-outline-quantum {
    border-color: rgba(0, 229, 255, 0.5);
    color: var(--consciousness-primary);
  }
  .btn-outline-quantum:hover {
    background-color: rgba(0, 229, 255, 0.1);
    border-color: var(--consciousness-primary);
    color: var(--consciousness-secondary);
    box-shadow: 0 10px 25px -5px rgba(0, 229, 255, 0.2);
  }
  .btn-outline-quantum:focus {
    --tw-ring-color: var(--consciousness-primary);
  }

  /* Division-Specific Button Styles */
  .btn-symbioautomate {
    background: linear-gradient(to right, var(--symbioautomate-primary), var(--symbioautomate-secondary));
    box-shadow: 0 10px 25px -5px var(--symbioautomate-glow);
    border-color: var(--symbioautomate-primary);
  }
  .btn-symbioautomate:hover {
    background: linear-gradient(to right, var(--symbioautomate-secondary), var(--symbioautomate-primary));
    box-shadow: 0 20px 40px -5px var(--symbioautomate-glow);
  }

  .btn-symbiolabs {
    background: linear-gradient(to right, var(--symbiolabs-primary), var(--symbiolabs-secondary));
    box-shadow: 0 10px 25px -5px var(--symbiolabs-glow);
    border-color: var(--symbiolabs-primary);
  }
  .btn-symbiolabs:hover {
    background: linear-gradient(to right, var(--symbiolabs-secondary), var(--symbiolabs-primary));
    box-shadow: 0 20px 40px -5px var(--symbiolabs-glow);
  }

  .btn-symbioxchange {
    background: linear-gradient(to right, var(--symbioxchange-primary), var(--symbioxchange-secondary));
    box-shadow: 0 10px 25px -5px var(--symbioxchange-glow);
    border-color: var(--symbioxchange-primary);
  }
  .btn-symbioxchange:hover {
    background: linear-gradient(to right, var(--symbioxchange-secondary), var(--symbioxchange-primary));
    box-shadow: 0 20px 40px -5px var(--symbioxchange-glow);
  }

  .btn-symbioedge {
    background: linear-gradient(to right, var(--symbioedge-primary), var(--symbioedge-secondary));
    box-shadow: 0 10px 25px -5px var(--symbioedge-glow);
    border-color: var(--symbioedge-primary);
  }
  .btn-symbioedge:hover {
    background: linear-gradient(to right, var(--symbioedge-secondary), var(--symbioedge-primary));
    box-shadow: 0 20px 40px -5px var(--symbioedge-glow);
  }

  .btn-symbioimpact {
    background: linear-gradient(to right, var(--symbioimpact-primary), var(--symbioimpact-secondary));
    box-shadow: 0 10px 25px -5px var(--symbioimpact-glow);
    border-color: var(--symbioimpact-primary);
  }
  .btn-symbioimpact:hover {
    background: linear-gradient(to right, var(--symbioimpact-secondary), var(--symbioimpact-primary));
    box-shadow: 0 20px 40px -5px var(--symbioimpact-glow);
  }

  .btn-symbioventures {
    background: linear-gradient(to right, var(--symbioventures-primary), var(--symbioventures-secondary));
    box-shadow: 0 10px 25px -5px var(--symbioventures-glow);
    border-color: var(--symbioventures-primary);
  }
  .btn-symbioventures:hover {
    background: linear-gradient(to right, var(--symbioventures-secondary), var(--symbioventures-primary));
    box-shadow: 0 20px 40px -5px var(--symbioventures-glow);
  }

  .btn-symbioalliance {
    background: linear-gradient(to right, var(--symbioalliance-primary), var(--symbioalliance-secondary));
    box-shadow: 0 10px 25px -5px var(--symbioalliance-glow);
    border-color: var(--symbioalliance-primary);
  }
  .btn-symbioalliance:hover {
    background: linear-gradient(to right, var(--symbioalliance-secondary), var(--symbioalliance-primary));
    box-shadow: 0 20px 40px -5px var(--symbioalliance-glow);
  }

  /* Enhanced Professional Styles */
  .btn-professional {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95));
    border: 1px solid rgba(148, 163, 184, 0.3);
    backdrop-filter: blur(16px);
    box-shadow:
      0 8px 32px -8px rgba(15, 23, 42, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.05) inset,
      0 1px 0 0 rgba(255, 255, 255, 0.1) inset;
    position: relative;
    overflow: hidden;
  }
  .btn-professional:hover {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.98), rgba(51, 65, 85, 0.98));
    border-color: rgba(148, 163, 184, 0.4);
    box-shadow:
      0 12px 48px -8px rgba(15, 23, 42, 0.5),
      0 0 0 1px rgba(255, 255, 255, 0.08) inset,
      0 1px 0 0 rgba(255, 255, 255, 0.15) inset;
    transform: translateY(-2px);
  }
  .btn-professional:active {
    transform: translateY(-1px);
    box-shadow:
      0 4px 16px -4px rgba(15, 23, 42, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.05) inset;
  }
  .btn-professional::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.8s ease;
  }
  .btn-professional:hover::before {
    left: 100%;
  }
`;

// Inject styles into document head
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = bioQuantumButtonStyles;
  document.head.appendChild(styleElement);
}

// Loading spinner component
const LoadingSpinner = ({ size = 'sm' }: { size?: 'sm' | 'md' | 'lg' }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };
  
  return (
    <svg
      className={cn('animate-spin', sizeClasses[size])}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );
};

// Button variants using class-variance-authority for type safety
const buttonVariants = cva(
  [
    // Base styles - foundational design with bio-quantum integration
    'relative inline-flex items-center justify-center',
    'font-semibold tracking-tight',
    'border border-transparent',
    'transition-all duration-medium ease-out', // Using quantum duration
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed',
    'select-none touch-manipulation',
    'overflow-hidden',
    'quantum-transform', // Bio-quantum utility class

    // Typography and spacing
    'whitespace-nowrap text-center',

    // Enhanced interaction states with quantum timing
    'active:scale-[0.98] active:transition-transform active:duration-fast',
    'anticipatory', // Bio-quantum anticipatory interaction

    // Accessibility improvements
    'focus-visible:ring-2 focus-visible:ring-offset-2',
  ],
  {
    variants: {
      variant: {
        // Bio-Quantum Primary - Consciousness Cyan-Teal
        primary: [
          'btn-primary',
          'text-white border-transparent',
          'transition-all duration-medium',
        ],

        // Quantum Variant - Core bio-quantum aesthetic
        quantum: [
          'btn-quantum',
          'text-white border-transparent',
          'backdrop-blur-sm',
          'transition-all duration-medium',
          'before:absolute before:inset-0',
          'before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-medium',
        ],

        // Outline Quantum - Transparent with quantum borders
        'outline-quantum': [
          'btn-outline-quantum',
          'bg-transparent border-2',
          'backdrop-blur-sm',
          'transition-all duration-medium',
        ],

        // Secondary - Professional glass morphism
        secondary: [
          'bg-gradient-to-r from-slate-800/90 to-slate-700/90',
          'hover:from-slate-700/95 hover:to-slate-600/95',
          'text-white border border-slate-600/50',
          'backdrop-blur-xl',
          'shadow-lg shadow-slate-900/25',
          'hover:shadow-xl hover:shadow-slate-900/30',
          'hover:-translate-y-0.5',
          'active:translate-y-0',
          'transition-all duration-300 ease-out',
          'focus:ring-2 focus:ring-slate-400/50 focus:ring-offset-2',
          'relative overflow-hidden',
          'before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/10 before:to-transparent',
          'before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-700',
        ],

        // Success - SymbioAutomate Emerald
        success: [
          'bg-gradient-to-r from-symbioautomate-primary to-symbioautomate-secondary',
          'hover:from-symbioautomate-secondary hover:to-symbioautomate-primary',
          'text-white border-symbioautomate-primary',
          'shadow-lg shadow-symbioautomate-primary/25',
          'hover:shadow-xl hover:shadow-symbioautomate-primary/30',
          'focus:ring-symbioautomate-primary',
        ],

        // Danger - Destructive with bio-quantum styling
        danger: [
          'bg-gradient-to-r from-destructive to-red-700',
          'hover:from-red-700 hover:to-red-800',
          'text-destructive-foreground border-destructive',
          'shadow-lg shadow-destructive/25',
          'hover:shadow-xl hover:shadow-destructive/30',
          'focus:ring-destructive',
        ],

        // Warning - SymbioLabs Amber
        warning: [
          'bg-gradient-to-r from-symbiolabs-primary to-symbiolabs-secondary',
          'hover:from-symbiolabs-secondary hover:to-symbiolabs-primary',
          'text-white border-symbiolabs-primary',
          'shadow-lg shadow-symbiolabs-primary/25',
          'hover:shadow-xl hover:shadow-symbiolabs-primary/30',
          'focus:ring-symbiolabs-primary',
        ],

        // Outline - Subtle borders with bio-quantum integration
        outline: [
          'bg-transparent hover:bg-accent',
          'text-foreground border-border',
          'hover:border-accent-foreground/20',
          'shadow-sm hover:shadow-md',
          'focus:ring-ring',
        ],

        // Ghost - Minimal with quantum hover effects
        ghost: [
          'bg-transparent hover:bg-accent',
          'text-foreground border-transparent',
          'hover:border-accent',
          'focus:ring-ring',
        ],

        // Professional - Enhanced glass morphism design
        professional: [
          'btn-professional',
          'text-white border-transparent',
          'transition-all duration-300 ease-out',
          'backdrop-blur-xl',
        ],

        // Link - Consciousness-themed links
        link: [
          'bg-transparent hover:bg-transparent',
          'text-consciousness-400 hover:text-consciousness-300',
          'border-transparent',
          'underline-offset-4 hover:underline',
          'focus:ring-consciousness-400',
          'shadow-none',
        ],

        // Division-Specific Variants
        symbioautomate: [
          'btn-symbioautomate',
          'text-white border-transparent',
          'transition-all duration-medium',
        ],

        symbiolabs: [
          'btn-symbiolabs',
          'text-white border-transparent',
          'transition-all duration-medium',
        ],

        symbioxchange: [
          'btn-symbioxchange',
          'text-white border-transparent',
          'transition-all duration-medium',
        ],

        symbioedge: [
          'btn-symbioedge',
          'text-white border-transparent',
          'transition-all duration-medium',
        ],

        symbioimpact: [
          'btn-symbioimpact',
          'text-white border-transparent',
          'transition-all duration-medium',
        ],

        symbioventures: [
          'btn-symbioventures',
          'text-white border-transparent',
          'transition-all duration-medium',
        ],

        symbioalliance: [
          'btn-symbioalliance',
          'text-white border-transparent',
          'transition-all duration-medium',
        ],
      },
      size: {
        xs: ['text-xs', 'px-2.5 py-1.5', 'rounded-morphic-subtle', 'min-h-[28px]'],
        sm: ['text-sm', 'px-3 py-2', 'rounded-md', 'min-h-[32px]'],
        md: ['text-sm', 'px-4 py-2.5', 'rounded-lg', 'min-h-[40px]'],
        lg: ['text-base', 'px-6 py-3', 'rounded-cellular', 'min-h-[44px]'],
        xl: ['text-lg', 'px-8 py-4', 'rounded-neural', 'min-h-[52px]'],
      },
      fullWidth: {
        true: 'w-full',
        false: 'w-auto',
      },
      morphic: {
        true: 'rounded-organic',
        false: '',
      },
    },
    defaultVariants: {
      variant: 'primary',
      size: 'md',
      fullWidth: false,
      morphic: false,
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  /** Icon to display on the left side */
  leftIcon?: React.ReactNode;
  /** Icon to display on the right side */
  rightIcon?: React.ReactNode;
  /** Loading state - shows spinner and disables interaction */
  isLoading?: boolean;
  /** Loading text to display when isLoading is true */
  loadingText?: string;
  /** Make button full width */
  fullWidth?: boolean;
  /** Apply morphic (organic) border radius */
  morphic?: boolean;
  /** Accessible label for screen readers */
  'aria-label'?: string;
  /** Additional description for complex buttons */
  'aria-describedby'?: string;
}

/**
 * Enterprise-grade Button component with comprehensive variant support,
 * accessibility features, and sophisticated styling
 */
const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      fullWidth = false,
      morphic = false,
      leftIcon,
      rightIcon,
      isLoading = false,
      loadingText,
      children,
      className,
      disabled,
      type = 'button',
      'aria-label': ariaLabel,
      'aria-describedby': ariaDescribedby,
      ...props
    },
    ref
  ) => {
    const isDisabled = disabled || isLoading;

    // Determine spinner size based on button size
    const spinnerSize = size === 'xs' || size === 'sm' ? 'sm' : size === 'xl' ? 'lg' : 'md';

    // Content to display - either loading state or normal content
    const buttonContent = isLoading ? (
      <>
        <LoadingSpinner size={spinnerSize} />
        {loadingText && <span className="ml-2">{loadingText}</span>}
      </>
    ) : (
      <>
        {leftIcon && (
          <span className="inline-flex items-center justify-center mr-2" aria-hidden="true">
            {leftIcon}
          </span>
        )}
        <span>{children}</span>
        {rightIcon && (
          <span className="inline-flex items-center justify-center ml-2" aria-hidden="true">
            {rightIcon}
          </span>
        )}
      </>
    );

    return (
      <button
        ref={ref}
        type={type}
        className={cn(
          buttonVariants({ variant, size, fullWidth, morphic }),
          className
        )}
        disabled={isDisabled}
        aria-label={ariaLabel}
        aria-describedby={ariaDescribedby}
        aria-disabled={isDisabled}
        {...props}
      >
        {buttonContent}
      </button>
    );
  }
);

Button.displayName = 'Button';

export { Button, buttonVariants };
export default Button;