import React from 'react';
import Header from '../components/organisms/Header';
import Footer from '../components/organisms/Footer';
import Typography from '../components/atoms/Typography';
import Card from '../components/atoms/Card';
import Button from '../components/atoms/Button';
import { 
  Shield, 
  Layers, 
  Network, 
  Target, 
  Zap, 
  Brain, 
  TrendingUp, 
  Clock, 
  Cpu, 
  Database,
  ArrowRight,
  CheckCircle,
  XCircle,
  AlertTriangle,
  BarChart3,
  Lightbulb,
  Lock
} from 'lucide-react';

const CompetitiveAdvantage: React.FC = () => {
  return (
    <div className="min-h-screen surface-void">
      <Header />
      
      <main className="pt-24">
        {/* Hero Section */}
        <div className="container mx-auto px-8 py-16">
          <div className="text-center mb-16">
            <Typography as="h1" variant="4xl" weight="bold" gradient="consciousness" className="mb-6">
              Competitive Advantage - Why ACI Wins
            </Typography>
            <Typography as="p" variant="xl" color="secondary" className="max-w-4xl mx-auto mb-8">
              The Paradigm Shift: Why ACI Dominates Traditional AI
            </Typography>
            <Typography as="p" variant="lg" color="tertiary" className="max-w-5xl mx-auto leading-relaxed">
              In a world where static AI models become obsolete within months, ACI represents a fundamental paradigm shift. 
              Our living intelligence doesn't just solve problems—it evolves with them.
            </Typography>
          </div>

          {/* Unbeatable Competitive Moat */}
          <Card variant="quantum" className="mb-16 p-12 border-consciousness/30">
            <div className="text-center mb-12">
              <Typography as="h2" variant="3xl" weight="bold" color="consciousness" className="mb-6">
                Unbeatable Competitive Moat
              </Typography>
              <Typography variant="lg" color="secondary" className="leading-relaxed mb-8">
                Our integrated ecosystem creates a self-reinforcing moat. Competitors might copy one division, 
                but they cannot replicate the synergistic value created by eight interconnected, 
                ACI-powered business units working as one.
              </Typography>
            </div>
            
            <div className="grid md:grid-cols-3 gap-8">
              <Card variant="consciousness" className="p-8 text-center border-consciousness/25">
                <div className="w-16 h-16 bg-consciousness/20 backdrop-blur-xl border border-consciousness/30 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Layers className="w-8 h-8 text-consciousness" />
                </div>
                <Typography variant="lg" weight="semibold" color="consciousness" className="mb-4">
                  Proprietary ACI Technology
                </Typography>
                <Typography variant="sm" color="secondary">
                  Nature-inspired AI that evolves beyond traditional models
                </Typography>
              </Card>
              
              <Card variant="creativity" className="p-8 text-center border-creativity/25">
                <div className="w-16 h-16 bg-creativity/20 backdrop-blur-xl border border-creativity/30 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Network className="w-8 h-8 text-creativity" />
                </div>
                <Typography variant="lg" weight="semibold" color="creativity" className="mb-4">
                  Ecosystem Network Effects
                </Typography>
                <Typography variant="sm" color="secondary">
                  Value increases exponentially with each new participant
                </Typography>
              </Card>
              
              <Card variant="intuition" className="p-8 text-center border-harmony/25">
                <div className="w-16 h-16 bg-harmony/20 backdrop-blur-xl border border-harmony/30 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Target className="w-8 h-8 text-harmony" />
                </div>
                <Typography variant="lg" weight="semibold" color="harmony" className="mb-4">
                  First-Mover Advantage
                </Typography>
                <Typography variant="sm" color="secondary">
                  Defining platform for the circular economy transition
                </Typography>
              </Card>
            </div>
          </Card>

          {/* Traditional AI Crisis */}
          <Card variant="neural" className="mb-16 p-12 border-consciousness/20">
            <Typography as="h2" variant="2xl" weight="bold" color="consciousness" className="mb-8">
              The Traditional AI Crisis
            </Typography>
            <Typography variant="lg" color="secondary" className="mb-8">
              Why Current AI is Fundamentally Limited
            </Typography>
            
            <div className="grid md:grid-cols-3 gap-8">
              <div className="space-y-4">
                <div className="flex items-center gap-3 mb-4">
                  <XCircle className="w-6 h-6 text-red-400" />
                  <Typography variant="lg" weight="semibold" color="primary">The Brittleness Problem</Typography>
                </div>
                <div className="space-y-3 text-sm">
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                    <Typography variant="sm" color="tertiary">Static Training: Models frozen at the point of training</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                    <Typography variant="sm" color="tertiary">Data Drift: Performance degrades as real-world conditions change</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                    <Typography variant="sm" color="tertiary">Expensive Updates: Requires complete retraining with massive computational costs</Typography>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-3 mb-4">
                  <AlertTriangle className="w-6 h-6 text-yellow-400" />
                  <Typography variant="lg" weight="semibold" color="primary">The Scale Problem</Typography>
                </div>
                <div className="space-y-3 text-sm">
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                    <Typography variant="sm" color="tertiary">GPU Dependency: Massive computational requirements</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                    <Typography variant="sm" color="tertiary">Energy Intensive: Unsustainable power consumption</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                    <Typography variant="sm" color="tertiary">Cost Explosion: Exponentially increasing costs</Typography>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-3 mb-4">
                  <XCircle className="w-6 h-6 text-red-400" />
                  <Typography variant="lg" weight="semibold" color="primary">The Adaptability Problem</Typography>
                </div>
                <div className="space-y-3 text-sm">
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                    <Typography variant="sm" color="tertiary">Domain Specificity: Cannot easily transfer between tasks</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                    <Typography variant="sm" color="tertiary">Novel Situation Failure: Poor performance on unprecedented scenarios</Typography>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                    <Typography variant="sm" color="tertiary">Innovation Bottleneck: New capabilities require months or years</Typography>
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* ACI Advantage Matrix */}
          <Card variant="quantum" className="mb-16 p-12 border-consciousness/30">
            <Typography as="h2" variant="2xl" weight="bold" color="consciousness" className="mb-8">
              The ACI Advantage Matrix
            </Typography>
            <Typography variant="lg" color="secondary" className="mb-8">
              How ACI Solves Every Traditional AI Problem
            </Typography>
            
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b border-consciousness/20">
                    <th className="text-left p-4">
                      <Typography variant="sm" weight="semibold" color="consciousness">Challenge</Typography>
                    </th>
                    <th className="text-left p-4">
                      <Typography variant="sm" weight="semibold" color="creativity">Traditional AI</Typography>
                    </th>
                    <th className="text-left p-4">
                      <Typography variant="sm" weight="semibold" color="harmony">ACI Solution</Typography>
                    </th>
                    <th className="text-left p-4">
                      <Typography variant="sm" weight="semibold" color="consciousness">Competitive Impact</Typography>
                    </th>
                  </tr>
                </thead>
                <tbody className="space-y-2">
                  <tr className="border-b border-consciousness/10">
                    <td className="p-4">
                      <Typography variant="sm" weight="medium" color="primary">Adaptability</Typography>
                    </td>
                    <td className="p-4">
                      <Typography variant="sm" color="tertiary">Static, requires retraining</Typography>
                    </td>
                    <td className="p-4">
                      <Typography variant="sm" color="secondary">Continuous real-time evolution</Typography>
                    </td>
                    <td className="p-4">
                      <Typography variant="sm" color="consciousness">10x faster response to market changes</Typography>
                    </td>
                  </tr>
                  <tr className="border-b border-consciousness/10">
                    <td className="p-4">
                      <Typography variant="sm" weight="medium" color="primary">Scalability</Typography>
                    </td>
                    <td className="p-4">
                      <Typography variant="sm" color="tertiary">Exponential compute costs</Typography>
                    </td>
                    <td className="p-4">
                      <Typography variant="sm" color="secondary">Linear scaling, distributed processing</Typography>
                    </td>
                    <td className="p-4">
                      <Typography variant="sm" color="consciousness">100x more cost-effective at scale</Typography>
                    </td>
                  </tr>
                  <tr className="border-b border-consciousness/10">
                    <td className="p-4">
                      <Typography variant="sm" weight="medium" color="primary">Resilience</Typography>
                    </td>
                    <td className="p-4">
                      <Typography variant="sm" color="tertiary">Single points of failure</Typography>
                    </td>
                    <td className="p-4">
                      <Typography variant="sm" color="secondary">Self-healing, decentralized architecture</Typography>
                    </td>
                    <td className="p-4">
                      <Typography variant="sm" color="consciousness">99.9% uptime vs. industry 95%</Typography>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </Card>

          {/* Five Insurmountable Moats */}
          <Card variant="neural" className="mb-16 p-12 border-consciousness/20">
            <Typography as="h2" variant="2xl" weight="bold" color="consciousness" className="mb-8">
              The Five Insurmountable Moats
            </Typography>
            <Typography variant="lg" color="secondary" className="mb-8">
              Why Competitors Cannot Replicate Our Advantage
            </Typography>

            <div className="space-y-8">
              <Card variant="consciousness" className="p-8 border-consciousness/25">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-consciousness/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Lock className="w-6 h-6 text-consciousness" />
                  </div>
                  <div>
                    <Typography variant="lg" weight="semibold" color="consciousness" className="mb-3">
                      Moat 1: Proprietary Technology & Foundational IP
                    </Typography>
                    <div className="space-y-2">
                      <div className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-consciousness mt-1 flex-shrink-0" />
                        <Typography variant="sm" color="secondary">15+ core patents in fractional calculus AI and bio-inspired computing</Typography>
                      </div>
                      <div className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-consciousness mt-1 flex-shrink-0" />
                        <Typography variant="sm" color="secondary">Mathematical framework requiring years of specialized research to replicate</Typography>
                      </div>
                      <div className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-consciousness mt-1 flex-shrink-0" />
                        <Typography variant="sm" color="secondary">First-mover advantage in the post-connectionist AI paradigm</Typography>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>

              <Card variant="creativity" className="p-8 border-creativity/25">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-creativity/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Database className="w-6 h-6 text-creativity" />
                  </div>
                  <div>
                    <Typography variant="lg" weight="semibold" color="creativity" className="mb-3">
                      Moat 2: Data Network Effects
                    </Typography>
                    <div className="space-y-2">
                      <div className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-creativity mt-1 flex-shrink-0" />
                        <Typography variant="sm" color="secondary">Exponential intelligence improvement with each new data source</Typography>
                      </div>
                      <div className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-creativity mt-1 flex-shrink-0" />
                        <Typography variant="sm" color="secondary">Cold start problem for competitors entering the market</Typography>
                      </div>
                      <div className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-creativity mt-1 flex-shrink-0" />
                        <Typography variant="sm" color="secondary">Cross-domain learning that strengthens all applications simultaneously</Typography>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>

              <Card variant="intuition" className="p-8 border-harmony/25">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-harmony/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <Network className="w-6 h-6 text-harmony" />
                  </div>
                  <div>
                    <Typography variant="lg" weight="semibold" color="harmony" className="mb-3">
                      Moat 3: Ecosystem Flywheel
                    </Typography>
                    <div className="space-y-2">
                      <div className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-harmony mt-1 flex-shrink-0" />
                        <Typography variant="sm" color="secondary">Eight synergistic divisions creating compound value</Typography>
                      </div>
                      <div className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-harmony mt-1 flex-shrink-0" />
                        <Typography variant="sm" color="secondary">Platform network effects making competitors' point solutions obsolete</Typography>
                      </div>
                      <div className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-harmony mt-1 flex-shrink-0" />
                        <Typography variant="sm" color="secondary">Integration complexity that raises switching costs exponentially</Typography>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </Card>

          {/* Call to Action */}
          <div className="text-center">
            <Card variant="consciousness" className="p-12 border-consciousness/25">
              <Typography as="h3" variant="2xl" weight="bold" color="consciousness" className="mb-6">
                Experience the ACI Advantage
              </Typography>
              <Typography variant="lg" color="secondary" className="mb-8 max-w-3xl mx-auto">
                See how ACI's revolutionary approach can transform your business operations and competitive position.
              </Typography>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  variant="quantum"
                  size="lg"
                  leftIcon={<Brain className="w-5 h-5" />}
                  rightIcon={<ArrowRight className="w-5 h-5" />}
                  onClick={() => window.location.href = '/aci-technology'}
                >
                  Explore ACI Technology
                </Button>

                <Button
                  variant="secondary"
                  size="lg"
                  leftIcon={<Network className="w-5 h-5" />}
                  onClick={() => window.location.href = '/ecosystem'}
                >
                  View Ecosystem
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default CompetitiveAdvantage;
